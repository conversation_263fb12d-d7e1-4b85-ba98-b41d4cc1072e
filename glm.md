# 安装 Langchain SDK

首先需要安装 Langchain 和对应的依赖包，请确保 langchain_community 的版本在 0.0.32 以上。

```bash
pip install langchain langchainhub httpx_sse
```

## 使用 Langchain ChatOpenAI

Langchain 的 `ChatOpenAI` 类是对 `OpenAI` SDK 的封装，可以更方便调用。这里展示了如何使用 `ChatOpenAI` 类来调用 `GLM-4` 模型。

```python
import os
from langchain_openai import ChatOpenAI
from langchain.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
)
from langchain.chains import LL<PERSON>hain
from langchain.memory import ConversationBufferMemory

llm = ChatOpenAI(
    temperature=0.95,
    model="glm-4",
    openai_api_key="your zhipuai api key",
    openai_api_base="https://open.bigmodel.cn/api/paas/v4/",
)

prompt = ChatPromptTemplate(
    messages=[
        SystemMessagePromptTemplate.from_template("You are a nice chatbot having a conversation with a human."),
        MessagesPlaceholder(variable_name="chat_history"),
        HumanMessagePromptTemplate.from_template("{question}"),
    ]
)

memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

conversation = LLMChain(
    llm=llm,
    prompt=prompt,
    verbose=True,
    memory=memory
)

conversation.invoke({"question": "tell me a joke"})
```

## 使用LangChain AgentExecutor

同时，GLM-4也能很好的接入到 Langchain 的 `AgentExecutor` 中，这里展示了如何使用 `AgentExecutor` 来调用 `GLM-4` 模型。

```python
from langchain import hub
from langchain.agents import AgentExecutor, create_react_agent
from langchain_community.tools.tavily_search import TavilySearchResults
import os

os.environ["TAVILY_API_KEY"] = "your tavily api key"
tools = [TavilySearchResults(max_results=2)]

prompt = hub.pull("hwchase17/react")  # Choose the LLM to use

agent = create_react_agent(llm, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
agent_executor.invoke({"input": "what is LangChain?"})
```